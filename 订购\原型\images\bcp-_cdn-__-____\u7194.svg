﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="392px" height="492px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="82px" y="1929px" width="392px" height="492px" filterUnits="userSpaceOnUse" id="filter104">
      <feOffset dx="5" dy="5" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="2.5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0.8  0 0 0 0 0.8  0 0 0 0 0.8  0 0 0 0.349019607843137 0  " in="shadowComposite" />
    </filter>
    <g id="widget105">
      <path d="M 0 463  L 0 0  L 382 0  L 382 463  L 50.6208588957056 463  L 36.5595092024541 481.650724637682  L 26.4853333333333 463  L 0 463  Z " fill-rule="nonzero" fill="#ffffff" stroke="none" transform="matrix(1 0 0 1 82 1929 )" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -82 -1929 )">
    <use xlink:href="#widget105" filter="url(#filter104)" />
    <use xlink:href="#widget105" />
  </g>
</svg>